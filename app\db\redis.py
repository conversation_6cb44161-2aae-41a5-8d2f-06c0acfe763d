"""
Redis缓存连接配置
"""

import json
import logging
from typing import Any, Optional, Union, List
import redis
from redis.connection import ConnectionPool
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from app.config import settings

logger = logging.getLogger(__name__)

# 创建Redis连接池
redis_pool = ConnectionPool(
    host=settings.redis_host,
    port=settings.redis_port,
    password=settings.redis_password if settings.redis_password else None,
    db=settings.redis_db,
    decode_responses=settings.redis_decode_responses,
    max_connections=settings.redis_max_connections,
    socket_timeout=settings.redis_socket_timeout,
    socket_connect_timeout=settings.redis_socket_connect_timeout,
    retry_on_timeout=True,
    health_check_interval=30,  # 健康检查间隔（秒）
)

# 创建Redis客户端
redis_client = redis.Redis(connection_pool=redis_pool)


class RedisManager:
    """Redis缓存管理器"""

    def __init__(self):
        self.client = redis_client
        self.default_expire = 3600  # 默认过期时间1小时

    def ping(self) -> bool:
        """测试Redis连接"""
        try:
            return self.client.ping()
        except Exception as e:
            logger.error(f"Redis连接测试失败: {e}")
            return False

    def get_info(self) -> dict:
        """获取Redis服务器信息"""
        try:
            return self.client.info()
        except Exception as e:
            logger.error(f"获取Redis信息失败: {e}")
            return {}

    def get_connection_info(self) -> dict:
        """获取连接池信息"""
        pool = self.client.connection_pool
        return {
            "max_connections": pool.max_connections,
            "created_connections": pool.created_connections,
            "available_connections": len(pool._available_connections),
            "in_use_connections": len(pool._in_use_connections),
        }

    # ============ 基础缓存操作 ============

    def set_cache(self, key: str, value: Any, expire: int = None) -> bool:
        """
        设置缓存

        Args:
            key: 缓存键
            value: 缓存值（支持字典、列表、字符串、数字等）
            expire: 过期时间（秒），默认使用实例设置

        Returns:
            是否设置成功
        """
        try:
            expire = expire or self.default_expire

            # 序列化复杂数据类型
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False, default=str)
            elif isinstance(value, (int, float)):
                value = str(value)

            return self.client.setex(key, expire, value)
        except Exception as e:
            logger.error(f"Redis设置缓存失败 [key: {key}]: {e}")
            return False

    def get_cache(self, key: str) -> Optional[Any]:
        """
        获取缓存

        Args:
            key: 缓存键

        Returns:
            缓存值，如果不存在返回None
        """
        try:
            value = self.client.get(key)
            if value is None:
                return None

            # 尝试解析JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                # 如果不是JSON，返回原始字符串
                return value
        except Exception as e:
            logger.error(f"Redis获取缓存失败 [key: {key}]: {e}")
            return None

    def delete_cache(self, key: str) -> bool:
        """删除缓存"""
        try:
            return self.client.delete(key) > 0
        except Exception as e:
            logger.error(f"Redis删除缓存失败 [key: {key}]: {e}")
            return False

    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return self.client.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis检查键存在失败 [key: {key}]: {e}")
            return False

    def expire(self, key: str, expire: int) -> bool:
        """设置键的过期时间"""
        try:
            return self.client.expire(key, expire)
        except Exception as e:
            logger.error(f"Redis设置过期时间失败 [key: {key}]: {e}")
            return False

    def ttl(self, key: str) -> int:
        """获取键的剩余过期时间"""
        try:
            return self.client.ttl(key)
        except Exception as e:
            logger.error(f"Redis获取TTL失败 [key: {key}]: {e}")
            return -1

    # ============ 批量操作 ============

    def mset(self, mapping: dict, expire: int = None) -> bool:
        """批量设置缓存"""
        try:
            expire = expire or self.default_expire

            # 序列化所有值
            serialized_mapping = {}
            for k, v in mapping.items():
                if isinstance(v, (dict, list)):
                    serialized_mapping[k] = json.dumps(
                        v, ensure_ascii=False, default=str
                    )
                else:
                    serialized_mapping[k] = str(v)

            # 批量设置
            result = self.client.mset(serialized_mapping)

            # 批量设置过期时间
            if result and expire:
                pipeline = self.client.pipeline()
                for key in serialized_mapping.keys():
                    pipeline.expire(key, expire)
                pipeline.execute()

            return result
        except Exception as e:
            logger.error(f"Redis批量设置缓存失败: {e}")
            return False

    def mget(self, keys: List[str]) -> dict:
        """批量获取缓存"""
        try:
            values = self.client.mget(keys)
            result = {}

            for i, key in enumerate(keys):
                value = values[i]
                if value is not None:
                    try:
                        result[key] = json.loads(value)
                    except (json.JSONDecodeError, TypeError):
                        result[key] = value
                else:
                    result[key] = None

            return result
        except Exception as e:
            logger.error(f"Redis批量获取缓存失败: {e}")
            return {}

    def delete_pattern(self, pattern: str) -> int:
        """根据模式删除缓存"""
        try:
            keys = self.client.keys(pattern)
            if keys:
                return self.client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Redis模式删除失败 [pattern: {pattern}]: {e}")
            return 0

    # ============ 业务专用方法 ============

    def set_route_matrix_cache(
        self, origins: str, destinations: str, data: dict, expire: int = 1800
    ) -> bool:
        """
        缓存路径矩阵结果

        Args:
            origins: 起点字符串
            destinations: 终点字符串
            data: 路径矩阵数据
            expire: 过期时间（秒），默认30分钟
        """
        cache_key = f"route_matrix:{hash(origins + destinations)}"
        return self.set_cache(cache_key, data, expire)

    def get_route_matrix_cache(self, origins: str, destinations: str) -> Optional[dict]:
        """获取路径矩阵缓存"""
        cache_key = f"route_matrix:{hash(origins + destinations)}"
        return self.get_cache(cache_key)

    def set_vehicle_location_cache(
        self, vehicle_id: int, location: dict, expire: int = 300
    ) -> bool:
        """
        缓存车辆位置信息

        Args:
            vehicle_id: 车辆ID
            location: 位置信息 {lat: float, lng: float, timestamp: int}
            expire: 过期时间（秒），默认5分钟
        """
        cache_key = f"vehicle_location:{vehicle_id}"
        return self.set_cache(cache_key, location, expire)

    def get_vehicle_location_cache(self, vehicle_id: int) -> Optional[dict]:
        """获取车辆位置缓存"""
        cache_key = f"vehicle_location:{vehicle_id}"
        return self.get_cache(cache_key)

    def set_dispatch_result_cache(
        self, request_id: str, result: dict, expire: int = 7200
    ) -> bool:
        """
        缓存调度结果

        Args:
            request_id: 请求ID
            result: 调度结果
            expire: 过期时间（秒），默认2小时
        """
        cache_key = f"dispatch_result:{request_id}"
        return self.set_cache(cache_key, result, expire)

    def get_dispatch_result_cache(self, request_id: str) -> Optional[dict]:
        """获取调度结果缓存"""
        cache_key = f"dispatch_result:{request_id}"
        return self.get_cache(cache_key)

    # ============ 管理方法 ============

    def clear_all_cache(self) -> bool:
        """清除所有缓存（慎用！）"""
        try:
            return self.client.flushdb()
        except Exception as e:
            logger.error(f"Redis清除所有缓存失败: {e}")
            return False

    def get_memory_usage(self) -> dict:
        """获取内存使用情况"""
        try:
            info = self.client.info("memory")
            return {
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "used_memory_peak": info.get("used_memory_peak", 0),
                "used_memory_peak_human": info.get("used_memory_peak_human", "0B"),
            }
        except Exception as e:
            logger.error(f"获取Redis内存使用情况失败: {e}")
            return {}


def test_redis_connection() -> bool:
    """测试Redis连接"""
    try:
        return redis_client.ping()
    except Exception as e:
        logger.error(f"Redis连接测试失败: {e}")
        return False


# 全局Redis管理器实例
redis_manager = RedisManager()
