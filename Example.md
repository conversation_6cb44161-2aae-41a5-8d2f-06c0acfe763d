---

### **项目名称：`dispatch_optimizer`**

### **根目录文件**

#### `pyproject.toml` (项目核心配置文件，Poetry 管理)

```toml
[tool.poetry]
name = "dispatch_optimizer"
version = "0.1.0"
description = "A smart vehicle dispatch optimization system."
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.10" # 指定Python版本
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlalchemy = "^2.0.23"
psycopg2-binary = "^2.9.9" # for PostgreSQL, use mysqlclient for MySQL
pydantic = {extras = ["email"], version = "^2.5.2"}
celery = "^5.3.6"
redis = "^5.0.1"
google-api-python-client = "^2.108.0" # 示例: 如果地图API有Python库
requests = "^2.31.0"           # 用于直接HTTP请求API
python-dotenv = "^1.0.0"       # 用于加载.env文件
ortools = "^9.8.3296"           # Google OR-Tools
python-geohash = "^0.8.5"        # 用于Geohash缓存

[tool.poetry.dev-dependencies]
pytest = "^7.4.3"
black = "^23.11.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
```

- **作用**：定义项目基本信息、所有生产和开发依赖。使用`poetry add <package>`或`poetry install`来管理。

#### `.env` (环境变量 - **绝不提交到 Git!**)

```env
# APP settings
PROJECT_NAME="Dispatch Optimizer"
API_V1_STR="/api/v1"

# Database settings
DATABASE_URL="**********************************/app"

# Celery settings
CELERY_BROKER_URL="redis://redis:6379/0"
CELERY_RESULT_BACKEND="redis://redis:6379/0"

# Map Service settings
MAP_API_KEY="YOUR_SECRET_MAP_API_KEY"
```

- **作用**：存放所有敏感信息和环境相关配置。

---

### **`app/` 目录详解**

#### `app/main.py` (FastAPI 应用入口)

```python
from fastapi import FastAPI
from .config import settings
from .routers import dispatch
from .db.session import engine # 确保数据库连接
from .models.base import Base # 导入所有模型

# (可选) 在启动时创建数据库表
# Base.metadata.create_all(bind=engine)

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# 包含API路由
app.include_router(dispatch.router, prefix=settings.API_V1_STR)

@app.get("/health")
def health_check():
    return {"status": "ok"}
```

- **作用**：创建 FastAPI 实例，加载配置，包含 API 路由，是整个 Web 服务的入口。

#### `app/config.py` (配置加载)

```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    PROJECT_NAME: str = "Dispatch Optimizer"
    API_V1_STR: str = "/api/v1"
    DATABASE_URL: str
    CELERY_BROKER_URL: str
    CELERY_RESULT_BACKEND: str
    MAP_API_KEY: str

    class Config:
        env_file = ".env"

settings = Settings()
```

- **作用**：使用 Pydantic 的`BaseSettings`，自动从`.env`文件加载配置，并提供类型校验。

#### `app/db/session.py` (数据库会话管理)

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from ..config import settings

engine = create_engine(settings.DATABASE_URL, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

- **作用**：创建数据库连接引擎和会话。`get_db`函数是一个依赖注入项，可以为每个 API 请求提供一个独立的数据库会話。

#### `app/models/` (数据库 ORM 模型)

- **`app/models/base.py`**:
  ```python
  from sqlalchemy.ext.declarative import declarative_base
  Base = declarative_base()
  # 以后所有模型都继承自这个Base
  ```
- **`app/models/order.py`**:

  ```python
  from sqlalchemy import Column, Integer, String, Float
  from .base import Base

  class Order(Base):
      __tablename__ = "orders"
      id = Column(Integer, primary_key=True, index=True)
      status = Column(String, index=True)
      start_lat = Column(Float)
      start_lon = Column(Float)
      passenger_count = Column(Integer)
      # ... 其他字段
  ```

#### `app/dto/` (API 数据模型)

- **`app/dto/order.py`**:

  ```python
  from pydantic import BaseModel

  class OrderBase(BaseModel):
      start_lat: float
      start_lon: float
      passenger_count: int

  class OrderCreate(OrderBase):
      pass

  class Order(OrderBase):
      id: int
      status: str

      class Config:
          from_attributes = True # 允许从ORM模型转换
  ```

- **作用**：定义 API 的输入（`Create`）和输出（`Order`）数据结构，由 FastAPI 用于自动校验和文档生成。

#### `app/crud/` (数据访问层)

- **`app/crud/crud_order.py`**:

  ```python
  from sqlalchemy.orm import Session
  from ..models.order import Order
  from ..dto.order import OrderCreate

  def get_unassigned_orders(db: Session):
      return db.query(Order).filter(Order.status == "unassigned").all()

  def create_order(db: Session, order: OrderCreate):
      db_order = Order(**order.dict())
      db.add(db_order)
      db.commit()
      db.refresh(db_order)
      return db_order
  ```

#### `app/services/` (业务逻辑层)

- **`app/services/map_service.py`**: (包含 Geohash 缓存)

  ```python
  import redis
  import requests
  import geohash

  class MapService:
      def __init__(self, api_key: str, redis_url: str):
          self.api_key = api_key
          self.cache = redis.from_url(redis_url)

      def get_travel_time(self, loc_start, loc_end):
          gh_start = geohash.encode(loc_start.lat, loc_start.lon, precision=7)
          gh_end = geohash.encode(loc_end.lat, loc_end.lon, precision=7)
          sorted_hashes = sorted([gh_start, gh_end])
          cache_key = f"time:{sorted_hashes[0]}:{sorted_hashes[1]}"

          cached_time = self.cache.get(cache_key)
          if cached_time:
              return int(cached_time)

          # ... API call logic ...
          # self.cache.set(...)
          return travel_time
  ```

#### `app/data_prep/` (数据准备与初筛)

- **`app/data_prep/matrix_builder.py`**:

  ```python
  import sys
  from ..services.map_service import MapService

  INFINITY = sys.maxsize

  class MatrixBuilder:
      def __init__(self, map_service: MapService):
          self.map_service = map_service

      def build(self, orders: list, vehicles: list, depots: list, destinations: list):
          nodes = self._define_nodes(orders, depots, destinations)
          time_matrix = [[INFINITY for _ in nodes] for _ in nodes]

          # ... 双层循环, 进行“时空可达性”初筛 ...
          # ... 对通过筛选的, 调用 self.map_service.get_travel_time ...

          # ... 打包成 data_for_solver 字典 ...
          return data
  ```

#### `app/core/` (核心算法)

- **`app/core/solver.py`**:

  ```python
  from ortools.constraint_solver import pywrapcp, routing_enums_pb2

  def solve_vrp(data: dict):
      # ... OR-Tools 建模与求解逻辑 ...
      return parsed_solution
  ```

#### `app/tasks/` (后台任务)

- **`app/tasks/celery_app.py`**: (Celery 应用实例)

  ```python
  from celery import Celery
  from ..config import settings

  celery_app = Celery(
      "tasks",
      broker=settings.CELERY_BROKER_URL,
      backend=settings.CELERY_RESULT_BACKEND
  )
  celery_app.conf.beat_schedule = {
      'run-dispatch-every-3-hours': {
          'task': 'app.tasks.dispatch.run_batch_dispatch_task',
          'schedule': 3600.0 * 3,
      },
  }
  ```

- **`app/tasks/dispatch.py`**: (具体的调度任务)

  ```python
  from .celery_app import celery_app
  from ..services.dispatch_service import DispatchService # 一个新的编排服务

  @celery_app.task
  def run_batch_dispatch_task():
      dispatch_service = DispatchService()
      dispatch_service.run_full_process()
  ```

#### `app/routers/` (API 路由)

- **`app/routers/dispatch.py`**:

  ```python
  from fastapi import APIRouter, Depends
  from sqlalchemy.orm import Session
  from ..db.session import get_db
  from ..tasks.dispatch import run_batch_dispatch_task

  router = APIRouter()

  @router.post("/dispatch/trigger", status_code=202)
  def trigger_dispatch_run():
      """手动触发一次后台批量调度任务"""
      task = run_batch_dispatch_task.delay()
      return {"message": "Dispatch task triggered.", "task_id": task.id}
  ```
