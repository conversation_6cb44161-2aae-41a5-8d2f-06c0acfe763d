"""
MySQL数据库连接配置
"""

import logging
from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import DisconnectionError
from app.config import settings

logger = logging.getLogger(__name__)

# 创建MySQL引擎
mysql_engine = create_engine(
    settings.mysql_url,
    poolclass=QueuePool,
    pool_size=settings.mysql_pool_size,
    max_overflow=settings.mysql_max_overflow,
    pool_timeout=settings.mysql_pool_timeout,
    pool_recycle=settings.mysql_pool_recycle,
    pool_pre_ping=True,  # 连接前测试连接是否有效
    echo=settings.debug,  # 开发环境下打印SQL语句
    echo_pool=settings.debug,  # 开发环境下打印连接池信息
)

# 创建Session工厂
MySQLSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=mysql_engine)

# 创建ORM基类
Base = declarative_base()


# 监听连接事件，用于连接池优化
@event.listens_for(mysql_engine, "connect")
def set_mysql_pragma(dbapi_connection, connection_record):
    """设置MySQL连接参数"""
    try:
        with dbapi_connection.cursor() as cursor:
            # 设置连接超时
            cursor.execute("SET SESSION wait_timeout=28800")  # 8小时
            cursor.execute("SET SESSION interactive_timeout=28800")  # 8小时
            # 设置字符集
            cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
            # 设置时区
            cursor.execute("SET time_zone='+08:00'")
    except Exception as e:
        logger.warning(f"设置MySQL连接参数失败: {e}")


@event.listens_for(mysql_engine, "checkout")
def ping_connection(dbapi_connection, connection_record, connection_proxy):
    """检查连接是否有效"""
    try:
        # 执行简单查询测试连接
        with dbapi_connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
    except Exception as exc:
        if isinstance(exc, DisconnectionError):
            logger.info("MySQL连接已断开，正在重新连接...")
            raise exc
        else:
            logger.error(f"MySQL连接测试失败: {exc}")
            raise exc


def get_mysql_db():
    """
    获取MySQL数据库会话
    依赖注入函数，用于FastAPI
    """
    db = MySQLSessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"MySQL数据库会话错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def create_mysql_tables():
    """创建所有MySQL表"""
    try:
        Base.metadata.create_all(bind=mysql_engine)
        logger.info("MySQL表创建成功")
    except Exception as e:
        logger.error(f"创建MySQL表失败: {e}")
        raise


def drop_mysql_tables():
    """删除所有MySQL表（慎用！）"""
    try:
        Base.metadata.drop_all(bind=mysql_engine)
        logger.warning("MySQL表已删除")
    except Exception as e:
        logger.error(f"删除MySQL表失败: {e}")
        raise


def test_mysql_connection():
    """测试MySQL连接"""
    try:
        with mysql_engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test")).fetchone()
            if result and result[0] == 1:
                logger.info("MySQL连接测试成功")
                return True
            else:
                logger.error("MySQL连接测试失败：查询结果异常")
                return False
    except Exception as e:
        logger.error(f"MySQL连接测试失败: {e}")
        return False


class MySQLManager:
    """MySQL数据库管理器"""

    def __init__(self):
        self.engine = mysql_engine
        self.SessionLocal = MySQLSessionLocal

    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()

    def execute_sql(self, sql: str, params: dict = None):
        """执行原生SQL"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(sql), params or {})
                return result.fetchall()
        except Exception as e:
            logger.error(f"执行SQL失败: {e}")
            raise

    def get_connection_info(self):
        """获取连接池信息"""
        pool = self.engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "total_connections": pool.size() + pool.overflow(),
        }


# 全局MySQL管理器实例
mysql_manager = MySQLManager()
