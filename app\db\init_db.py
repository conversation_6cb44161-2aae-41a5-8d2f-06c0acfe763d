"""
数据库初始化脚本
"""

import logging
from app.db.mysql import create_mysql_tables, test_mysql_connection, mysql_engine
from app.db.redis import test_redis_connection, redis_manager
from app.config import settings

logger = logging.getLogger(__name__)


def init_mysql():
    """初始化MySQL数据库"""
    try:
        # 测试连接
        if not test_mysql_connection():
            raise Exception("MySQL连接测试失败")

        # 创建表
        create_mysql_tables()
        logger.info("MySQL数据库初始化成功")
        return True

    except Exception as e:
        logger.error(f"MySQL数据库初始化失败: {e}")
        return False


def init_redis():
    """初始化Redis缓存"""
    try:
        # 测试连接
        if not test_redis_connection():
            raise Exception("Redis连接测试失败")

        # 获取Redis信息
        info = redis_manager.get_info()
        if info:
            logger.info(f"Redis服务器版本: {info.get('redis_version', 'unknown')}")
            logger.info(f"Redis内存使用: {info.get('used_memory_human', 'unknown')}")

        logger.info("Redis缓存初始化成功")
        return True

    except Exception as e:
        logger.error(f"Redis缓存初始化失败: {e}")
        return False


def init_databases():
    """初始化所有数据库"""
    logger.info("开始初始化数据库...")

    mysql_ok = init_mysql()
    redis_ok = init_redis()

    if mysql_ok and redis_ok:
        logger.info("所有数据库初始化成功")
        return True
    else:
        if not mysql_ok:
            logger.error("MySQL初始化失败")
        if not redis_ok:
            logger.error("Redis初始化失败")
        return False


def close_databases():
    """关闭数据库连接"""
    try:
        # 关闭MySQL连接池
        mysql_engine.dispose()
        logger.info("MySQL连接池已关闭")

        # Redis连接会自动管理，这里只记录
        logger.info("Redis连接已关闭")

    except Exception as e:
        logger.error(f"关闭数据库连接失败: {e}")


def check_database_health():
    """检查数据库健康状态"""
    health_status = {"mysql": False, "redis": False, "overall": False}

    # 检查MySQL
    try:
        health_status["mysql"] = test_mysql_connection()
    except Exception as e:
        logger.error(f"MySQL健康检查失败: {e}")

    # 检查Redis
    try:
        health_status["redis"] = test_redis_connection()
    except Exception as e:
        logger.error(f"Redis健康检查失败: {e}")

    # 总体健康状态
    health_status["overall"] = health_status["mysql"] and health_status["redis"]

    return health_status


def get_database_status():
    """获取数据库状态信息"""
    status = {"mysql": {}, "redis": {}, "health": check_database_health()}

    # MySQL状态
    try:
        from app.db.mysql import mysql_manager

        status["mysql"] = {
            "connection_info": mysql_manager.get_connection_info(),
            "url": settings.mysql_url.replace(f":{settings.mysql_password}@", ":***@")
            if settings.mysql_password
            else settings.mysql_url,
            "pool_size": settings.mysql_pool_size,
            "max_overflow": settings.mysql_max_overflow,
        }
    except Exception as e:
        logger.error(f"获取MySQL状态失败: {e}")
        status["mysql"]["error"] = str(e)

    # Redis状态
    try:
        status["redis"] = {
            "connection_info": redis_manager.get_connection_info(),
            "memory_usage": redis_manager.get_memory_usage(),
            "url": settings.redis_url,
            "max_connections": settings.redis_max_connections,
        }
    except Exception as e:
        logger.error(f"获取Redis状态失败: {e}")
        status["redis"]["error"] = str(e)

    return status


if __name__ == "__main__":
    """直接运行此文件进行数据库初始化"""

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # 初始化数据库
    success = init_databases()

    if success:
        print("✅ 数据库初始化成功")

        # 显示状态信息
        status = get_database_status()
        print("\n📊 数据库状态:")
        print(f"MySQL: {'✅' if status['health']['mysql'] else '❌'}")
        print(f"Redis: {'✅' if status['health']['redis'] else '❌'}")

    else:
        print("❌ 数据库初始化失败")
        exit(1)
