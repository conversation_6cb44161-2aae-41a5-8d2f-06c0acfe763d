import logging
from typing import Dict, List, Optional, Tuple, Any

from app.utils.http_client import HttpClient
from app.config import settings

logger = logging.getLogger(__name__)


class MapServiceError(Exception):
    """地图服务异常"""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(message)


class BaiduMapService:
    """百度地图服务 - 只负责调用API返回原始数据"""

    # API状态码说明
    STATUS_CODES = {
        0: "正常",
        1: "服务器内部错误",
        2: "请求参数非法",
        3: "权限校验失败",
        4: "配额校验失败",
        5: "ak不存在或者非法",
        101: "服务禁用",
        102: "不通过白名单或者安全码不对",
        200: "无权限",
        300: "配额错误",
    }

    def __init__(self, ak: str = None, base_url: str = None):
        """
        初始化百度地图服务

        Args:
            ak: 百度地图API密钥，默认从配置读取
            base_url: API基础URL，默认从配置读取
        """
        self.ak = ak or settings.baidu_map_ak
        self.base_url = base_url or settings.baidu_map_base_url

        if not self.ak:
            raise MapServiceError("百度地图API密钥未配置")

        self.client = HttpClient(
            base_url=self.base_url,
            timeout=30,
            headers={
                "User-Agent": "DispatchOptimizer/1.0",
                "Accept": "application/json",
            },
        )

        logger.info(f"百度地图服务初始化完成，AK: {self.ak[:8]}...")

    def _check_response(self, response_data: Dict) -> None:
        """检查API响应状态"""
        status = response_data.get("status", -1)
        message = response_data.get("message", "未知错误")

        if status != 0:
            status_desc = self.STATUS_CODES.get(status, "未知状态码")
            error_msg = f"{message} ({status_desc})"
            logger.error(
                f"百度地图API错误: 状态码={status}, 消息={message}, 描述={status_desc}"
            )
            raise MapServiceError(error_msg, status, response_data)

    def get_driving_route_matrix(
        self,
        origins: List[Tuple[float, float]],
        destinations: List[Tuple[float, float]],
        tactics: int = 11,
        coord_type: str = "bd09ll",
    ) -> Dict[str, Any]:
        """
        获取驾车路径矩阵 - 返回百度API原始数据

        Args:
            origins: 起点坐标列表 [(lat, lng), ...]
            destinations: 终点坐标列表 [(lat, lng), ...]
            tactics: 路线策略，默认11（常规路线）
                    10: 不走高速
                    11: 常规路线
                    12: 距离较短
                    13: 时间较短
            coord_type: 坐标类型，默认bd09ll（百度经纬度坐标）

        Returns:
            百度API原始响应数据

        Raises:
            MapServiceError: API调用失败
            ValueError: 参数错误
        """
        # 参数验证
        if not origins or not destinations:
            raise ValueError("起点和终点坐标不能为空")

        if len(origins) > 200 or len(destinations) > 200:
            raise ValueError("起点和终点数量不能超过200个")

        # 格式化坐标
        origins_str = "|".join([f"{lat},{lng}" for lat, lng in origins])
        destinations_str = "|".join([f"{lat},{lng}" for lat, lng in destinations])

        # 构建请求参数
        params = {
            "origins": origins_str,
            "destinations": destinations_str,
            "tactics": tactics,
            "coord_type": coord_type,
            "ak": self.ak,
            "output": "json",
        }

        logger.info(
            f"请求百度地图API: {len(origins)}个起点 -> {len(destinations)}个终点"
        )
        logger.debug(f"请求参数: {params}")

        try:
            # 发起API请求
            response_data = self.client.get_json(
                "routematrix/v2/driving", params=params
            )

            # 检查响应状态
            self._check_response(response_data)

            logger.info("百度地图API调用成功")
            return response_data

        except Exception as e:
            logger.error(f"百度地图API调用失败: {e}")
            raise MapServiceError(f"API调用失败: {e}")
