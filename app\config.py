from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # 应用配置
    debug: bool = True
    log_level: str = "INFO"

    # 日志配置
    log_to_file: bool = True
    log_to_console: bool = True
    log_file_path: str = "logs/app.log"
    log_max_file_size: int = 10 * 1024 * 1024  # 10MB
    log_backup_count: int = 5

    # 百度地图API配置
    baidu_map_ak: str = "SAPPPcbp4EldN1fsOYAGY1mSgzuJFnue"  # 百度地图API密钥
    baidu_map_base_url: str = "https://api.map.baidu.com"

    # MySQL数据库配置
    mysql_host: str = "localhost"
    mysql_port: int = 3306
    mysql_user: str = "root"
    mysql_password: str = ""
    mysql_database: str = "dispatch_optimizer"
    mysql_charset: str = "utf8mb4"

    # MySQL连接池配置
    mysql_pool_size: int = 10
    mysql_max_overflow: int = 20
    mysql_pool_timeout: int = 30
    mysql_pool_recycle: int = 3600  # 1小时

    # Redis配置
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_password: str = ""
    redis_db: int = 0
    redis_decode_responses: bool = True

    # Redis连接池配置
    redis_max_connections: int = 50
    redis_socket_timeout: int = 5
    redis_socket_connect_timeout: int = 5

    # 调度算法配置
    assumed_min_speed_kmh: int = 30
    time_difference_threshold_minutes: int = 90
    straight_line_distance_threshold_km: int = 15

    # Celery配置
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"

    @property
    def mysql_url(self) -> str:
        """生成MySQL连接URL"""
        return f"mysql+pymysql://{self.mysql_user}:{self.mysql_password}@{self.mysql_host}:{self.mysql_port}/{self.mysql_database}?charset={self.mysql_charset}"

    @property
    def redis_url(self) -> str:
        """生成Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        else:
            return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"

    class Config:
        env_file = ".env"
        extra = "ignore"


settings = Settings()
