"""
简单的地图服务使用示例

演示如何：
1. 调用百度地图API获取原始数据
2. 使用data_prep处理数据
"""

from app.services import BaiduMapService, MapServiceError

from app.config import settings


def main():
    print("=== 地图服务示例 ===")

    # 检查配置
    if not settings.baidu_map_ak:
        print("错误：请配置百度地图API密钥")
        return

    # 创建地图服务实例
    map_service = BaiduMapService()

    # 示例坐标（北京的一些地点）
    vehicles = [
        (39.916527, 116.397128),  # 天安门
        (39.989612, 116.480972),  # 鸟巢
    ]

    orders = [
        (39.906901, 116.397437),  # 前门
        (39.928353, 116.388285),  # 王府井
        (39.991957, 116.326434),  # 清华大学
    ]

    try:
        print("1. 调用百度地图API...")

        # 调用API获取原始数据
        raw_data = map_service.get_driving_route_matrix(vehicles, orders)

        print("2. 处理API响应数据...")

        print("3. 显示处理结果...")

        print(raw_data)
    except MapServiceError as e:
        print(f"地图服务错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")


if __name__ == "__main__":
    main()
