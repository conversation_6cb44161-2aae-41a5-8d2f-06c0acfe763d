from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from app.config import settings
from app.routers import dispatch
from app.utils.logger import setup_logging
import logging

# 导入数据库相关
from app.db import init_databases, close_databases, check_database_health

# 初始化日志系统
setup_logging(
    log_level=settings.log_level,
    enable_console=settings.log_to_console,
    enable_file=settings.log_to_file,
    log_file=settings.log_file_path.split("/")[-1],  # 获取文件名
    log_dir="/".join(settings.log_file_path.split("/")[:-1]) or "logs",  # 获取目录
    max_file_size=settings.log_max_file_size,
    backup_count=settings.log_backup_count,
)
logger = logging.getLogger(__name__)

# 初始化地图服务（延迟初始化，在需要时才创建）
if settings.baidu_map_ak:
    logger.info("百度地图API密钥已配置，服务将在需要时初始化")
else:
    logger.warning("百度地图API密钥未配置，相关功能将不可用")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("应用启动中...")
    if init_databases():
        logger.info("数据库初始化成功")
    else:
        logger.error("数据库初始化失败")

    yield

    # 关闭时执行
    logger.info("应用关闭中...")
    close_databases()
    logger.info("数据库连接已关闭")


app = FastAPI(
    title="调度优化器 API",
    description="车辆调度优化系统",
    version="1.0.0",
    # 使用settings中的配置
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan,
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(dispatch.router, prefix="/api/v1")


@app.get("/")
async def root():
    logger.info("根路径被访问")
    return {
        "message": "调度优化器 API 运行中",
        "version": "1.0.0",
        "debug": settings.debug,
    }


@app.get("/health")
def health_check():
    """健康检查接口"""
    from datetime import datetime

    health = check_database_health()

    return {
        "status": "healthy" if health["overall"] else "unhealthy",
        "mysql": "up" if health["mysql"] else "down",
        "redis": "up" if health["redis"] else "down",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
    }


@app.get("/config")
async def get_config():
    """获取当前配置信息（仅调试模式可用）"""
    if not settings.debug:
        return {"message": "配置信息仅在调试模式可用"}

    return {
        "database": {
            "host": settings.mysql_host,
            "port": settings.mysql_port,
            "database": settings.mysql_database,
        },
        "redis": {"host": settings.redis_host, "port": settings.redis_port},
        "algorithm": {
            "min_speed_kmh": settings.assumed_min_speed_kmh,
            "time_threshold_minutes": settings.time_difference_threshold_minutes,
            "distance_threshold_km": settings.straight_line_distance_threshold_km,
        },
    }
