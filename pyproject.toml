[tool.poetry]
name = "dispatch-optimizer"
version = "0.1.0"
description = "车辆调度优化系统"
authors = ["huangpeng <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.13"
fastapi = {extras = ["all"], version = "^0.116.1"}
celery = {extras = ["redis"], version = "^5.5.3"}
ortools = "^9.14.6206"
pymysql = "^1.1.0"
sqlalchemy = "^2.0.0"
redis = "^5.0.0"
requests = "^2.31.0"
pygeohash = "^3.2.0"
pandas = "^2.3.1"
python-json-logger = "^3.3.0"
pydantic-settings = "^2.10.1"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
ruff = "^0.12.7"

