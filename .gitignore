# ===========================================
# Python
# ===========================================
__pycache__/
*.py[cod]
*.so
.Python
build/ 
dist/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
.env
.venv
env/
venv/
ENV/

# 测试
.pytest_cache/
.coverage
htmlcov/

# ===========================================
# IDE
# ===========================================
.vscode/
.idea/
*.swp
*.swo

# ===========================================
# OS
# ===========================================
.DS_Store
Thumbs.db

# ===========================================
# 项目特定
# ===========================================
# 环境变量文件
.env
.env.local

# 日志文件和目录
*.log
logs/*

# 临时文件
temp/
tmp/
*.tmp

# 数据库文件
*.db
*.sqlite 
app/__pycache__/config.cpython-313.pyc
app/__pycache__/main.cpython-313.pyc
