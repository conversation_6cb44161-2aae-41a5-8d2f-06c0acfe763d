import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional

from app.config import settings


def setup_logging(
    log_level: Optional[str] = None,
    log_file: Optional[str] = None,
    log_dir: str = "logs",
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_console: bool = True,
    enable_file: bool = True,
):
    """
    配置应用日志系统

    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件名，默认为app.log
        log_dir: 日志目录，默认为logs
        max_file_size: 单个日志文件最大大小（字节）
        backup_count: 保留的日志文件数量
        enable_console: 是否启用控制台输出
        enable_file: 是否启用文件输出
    """
    # 获取日志级别
    if log_level is None:
        log_level = settings.log_level

    # 设置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建日志格式
    console_formatter = logging.Formatter(
        fmt="%(asctime)s | %(levelname)-8s | %(name)s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    file_formatter = logging.Formatter(
        fmt="%(asctime)s | %(levelname)-8s | %(name)s | %(filename)s:%(lineno)d | %(funcName)s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # 配置控制台处理器
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

    # 配置文件处理器
    if enable_file:
        # 创建日志目录
        log_path = Path(log_dir)
        log_path.mkdir(exist_ok=True)

        # 设置日志文件名
        if log_file is None:
            log_file = "app.log"

        file_path = log_path / log_file

        # 使用RotatingFileHandler进行日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            filename=file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding="utf-8",
        )
        file_handler.setLevel(logging.DEBUG)  # 文件记录所有级别的日志
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)

    # 设置第三方库的日志级别
    _configure_third_party_loggers()

    # 记录日志配置信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已配置 - 级别: {log_level}")
    if enable_console:
        logger.info("控制台日志已启用")
    if enable_file:
        logger.info(f"文件日志已启用 - 路径: {file_path}")


def _configure_third_party_loggers():
    """配置第三方库的日志级别"""
    # httpx日志级别设置
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)

    # uvicorn日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)

    # sqlalchemy日志级别（如果使用）
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)

    # celery日志级别（如果使用）
    logging.getLogger("celery").setLevel(logging.INFO)


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器

    Args:
        name: 日志记录器名称，通常使用 __name__

    Returns:
        配置好的日志记录器实例
    """
    return logging.getLogger(name)


# 为HTTP客户端创建专用的日志记录器
def setup_http_client_logging(enable_debug: bool = None):
    """
    为HTTP客户端配置专门的日志设置

    Args:
        enable_debug: 是否启用调试级别的HTTP日志
    """
    if enable_debug is None:
        enable_debug = settings.debug

    http_logger = logging.getLogger("app.utils.http_client")

    if enable_debug:
        http_logger.setLevel(logging.DEBUG)
    else:
        http_logger.setLevel(logging.INFO)

    return http_logger


# 日志工具函数
def log_function_call(func):
    """装饰器：记录函数调用日志"""
    import functools

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise

    return wrapper


def log_execution_time(func):
    """装饰器：记录函数执行时间"""
    import functools
    import time

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()

        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"函数 {func.__name__} 执行失败 (耗时: {execution_time:.3f}秒): {e}"
            )
            raise

    return wrapper
