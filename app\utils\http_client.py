import requests
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class HttpClient:
    """基于requests的HTTP客户端"""

    def __init__(
        self,
        base_url: Optional[str] = None,
        timeout: int = 30,
        headers: Optional[Dict[str, str]] = None,
        verify_ssl: bool = True,
    ):
        """
        初始化HTTP客户端

        Args:
            base_url: 基础URL
            timeout: 超时时间（秒）
            headers: 默认请求头
            verify_ssl: 是否验证SSL证书
        """
        self.base_url = base_url.rstrip("/") if base_url else None
        self.timeout = timeout
        self.verify_ssl = verify_ssl

        # 默认请求头
        self.default_headers = {
            "User-Agent": "HttpClient/1.0",
            "Accept": "application/json",
            "Content-Type": "application/json",
        }
        if headers:
            self.default_headers.update(headers)

    def _build_url(self, url: str) -> str:
        """构建完整URL"""
        if self.base_url and not url.startswith("http"):
            return f"{self.base_url}/{url.lstrip('/')}"
        return url

    def _merge_headers(self, headers: Optional[Dict[str, str]]) -> Dict[str, str]:
        """合并请求头"""
        merged = self.default_headers.copy()
        if headers:
            merged.update(headers)
        return merged

    def get(
        self, url: str, params: Optional[Dict] = None, headers: Optional[Dict] = None
    ) -> requests.Response:
        """发起GET请求"""
        full_url = self._build_url(url)
        merged_headers = self._merge_headers(headers)

        logger.debug(f"发起GET请求: {full_url}")
        response = requests.get(
            full_url,
            params=params,
            headers=merged_headers,
            timeout=self.timeout,
            verify=self.verify_ssl,
        )
        response.raise_for_status()
        return response

    def post(
        self,
        url: str,
        json: Optional[Dict] = None,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> requests.Response:
        """发起POST请求"""
        full_url = self._build_url(url)
        merged_headers = self._merge_headers(headers)

        logger.debug(f"发起POST请求: {full_url}")
        response = requests.post(
            full_url,
            json=json,
            data=data,
            params=params,
            headers=merged_headers,
            timeout=self.timeout,
            verify=self.verify_ssl,
        )
        response.raise_for_status()
        return response

    def put(
        self,
        url: str,
        json: Optional[Dict] = None,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> requests.Response:
        """发起PUT请求"""
        full_url = self._build_url(url)
        merged_headers = self._merge_headers(headers)

        logger.debug(f"发起PUT请求: {full_url}")
        response = requests.put(
            full_url,
            json=json,
            data=data,
            params=params,
            headers=merged_headers,
            timeout=self.timeout,
            verify=self.verify_ssl,
        )
        
        response.raise_for_status()
        return response

    def delete(
        self, url: str, params: Optional[Dict] = None, headers: Optional[Dict] = None
    ) -> requests.Response:
        """发起DELETE请求"""
        full_url = self._build_url(url)
        merged_headers = self._merge_headers(headers)

        logger.debug(f"发起DELETE请求: {full_url}")
        response = requests.delete(
            full_url,
            params=params,
            headers=merged_headers,
            timeout=self.timeout,
            verify=self.verify_ssl,
        )
        response.raise_for_status()
        return response

    def get_json(self, url: str, **kwargs) -> Dict[str, Any]:
        """发起GET请求并返回JSON数据"""
        response = self.get(url, **kwargs)
        return response.json()

    def post_json(self, url: str, **kwargs) -> Dict[str, Any]:
        """发起POST请求并返回JSON数据"""
        response = self.post(url, **kwargs)
        return response.json()

    def put_json(self, url: str, **kwargs) -> Dict[str, Any]:
        """发起PUT请求并返回JSON数据"""
        response = self.put(url, **kwargs)
        return response.json()


# 便捷函数
def get(url: str, **kwargs) -> requests.Response:
    """发起GET请求"""
    return requests.get(url, **kwargs)


def post(url: str, **kwargs) -> requests.Response:
    """发起POST请求"""
    return requests.post(url, **kwargs)


def put(url: str, **kwargs) -> requests.Response:
    """发起PUT请求"""
    return requests.put(url, **kwargs)


def delete(url: str, **kwargs) -> requests.Response:
    """发起DELETE请求"""
    return requests.delete(url, **kwargs)


def get_json(url: str, **kwargs) -> Dict[str, Any]:
    """发起GET请求并返回JSON数据"""
    response = requests.get(url, **kwargs)
    response.raise_for_status()
    return response.json()


def post_json(url: str, **kwargs) -> Dict[str, Any]:
    """发起POST请求并返回JSON数据"""
    response = requests.post(url, **kwargs)
    response.raise_for_status()
    return response.json()


def put_json(url: str, **kwargs) -> Dict[str, Any]:
    """发起PUT请求并返回JSON数据"""
    response = requests.put(url, **kwargs)
    response.raise_for_status()
    return response.json()
