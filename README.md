## 🏛️ 项目架构与目录结构

```
dispatch_optimizer/
├── .env                      # 环境变量
├── docker-compose.yml        # Docker服务编排
├── Dockerfile                # 应用主镜像
├── poetry.lock               # 精确依赖锁定
├── pyproject.toml            # 项目配置与依赖 (Poetry)
└── README.md                 # 本文件
└── app/                      # 应用代码主包
    ├── main.py               # FastAPI应用入口与全局配置
    ├── config.py             # 配置加载
    ├── routers/              # API路由层
    ├── core/                 # 核心算法 (OR-Tools Solver)
    ├── crud/                 # 数据访问层 (DAO)
    ├── data_prep/            # 数据准备与初筛
    ├── db/                   # 数据库会话管理
    ├── models/               # 数据库ORM模型 (SQLAlchemy)
    ├── dto/              # API数据模型 (Pydantic)
    ├── services/             # 业务逻辑与外部服务封装
    ├── tasks/                # 后台任务定义 (Celery)
    └── utils/                # 通用工具
```

### 模块职责详解

- **`app/main.py`**: 创建 FastAPI 应用实例，并挂载所有 API 路由。
- **`app/routers/`**: 定义所有 HTTP API 端点，负责处理外部请求和响应。
- **`app/services/`**: 封装核心业务逻辑，编排不同模块以完成一个完整的业务流程（如`dispatch_service`）。
- **`app/core/`**: 存放纯粹的、与业务无关的核心算法，如 OR-Tools 求解器。
- **`app/crud/`**: 数据访问对象 (DAO)，封装所有与数据库的直接交互 (CRUD 操作)。
- **`app/models/`**: 使用 SQLAlchemy 定义数据库的表结构。
- **`app/dto/`**: 使用 Pydantic 定义 API 的数据契约，用于数据校验和序列化。
- **`app/tasks/`**: 定义 Celery 后台任务，用于处理耗时的计算（如调度）。
- **`app/data_prep/`**: 负责在求解前，对原始数据进行清洗、预处理、初筛和矩阵构建。

## 🛠️ 开发与测试

- **激活虚拟环境**: `poetry env activate`
- **运行开发服务器**: `uvicorn app.main:app --reload`
